<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<activePackageSource>
		<add key="nuget.org" value="https://api.nuget.org/v3/index.json" />
	</activePackageSource>
	<packageSources>
		<add key="nuget.org" value="https://api.nuget.org/v3/index.json" protocolVersion="3" />
		<add key="libraries" value="https://interflorad365fo.pkgs.visualstudio.com/_packaging/ITF.SharedLibraries/nuget/v3/index.json" />
	</packageSources>
	<packageSourceCredentials>
		<libraries>
			<add key="Username" value="AnyLogin" />
			<add key="ClearTextPassword" value="9cMDAEkPb4kJ3QM3wvHfOqaninq1eiX0L9WEqODCyhuLRSdMGV3jJQQJ99BIACAAAAABtWubAAASAZDON33n" />
		</libraries>
	</packageSourceCredentials>
</configuration>