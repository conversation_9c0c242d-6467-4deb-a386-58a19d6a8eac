global using CSharpFunctionalExtensions;
using Elastic.Apm.SerilogEnricher;
using Elastic.CommonSchema;
using Elastic.CommonSchema.Serilog;
using MongoDB.Bson.IO;
using Serilog;
using Serilog.Sinks.Elasticsearch;
using System.Text.Json;
using Agent = Elastic.Apm.Agent;

namespace ApiAotTest.Infrastructure;

public record ConfigurationLogProvider(string? Seq = null, string? ElasticSearchLog = null);

public static class HostSettings
{
    public static string GetStringOrThrow(this IConfiguration config, string configName)
    {
        var configVar = config.GetValue<string>(configName);
        if (string.IsNullOrWhiteSpace(configVar))
            throw new Exception($"The application can't run, mandatory variables ({configName}) is not available from IConfiguration !");

        return configVar;
    }

    public static string GetString(this IConfiguration config, string configName) =>
        string.IsNullOrWhiteSpace(config.GetValue<string>(configName)) ? null : config.GetValue<string>(configName);

    public static T? Get<T>(this IConfiguration config, string configName)
    {
        var result = config.GetSection(configName).Get<T>();

        if (result != null)
            return result;

        var enVarStr = config.GetStringOrThrow(configName);
        var typeInfo = AppJsonSerializerContext.Default.GetTypeInfo(typeof(T));
        if (typeInfo == null)
            throw new InvalidOperationException($"Type {typeof(T).Name} is not registered in AppJsonSerializerContext. Add [JsonSerializable(typeof({typeof(T).Name}))] to the context.");

        return (T?)JsonSerializer.Deserialize(enVarStr, typeInfo);
    }

    public static IHostBuilder ConfigureHostBuilder(this IHostBuilder host) =>
        host.UseDefaultServiceProvider((_, opts) => opts.ValidateScopes = true)
        .ConfigureAppConfiguration((_, conf) =>
        {
            Result.Try(() =>
            {
                if (Directory.Exists("settings/"))
                    Directory.GetFiles("settings/", "*.json").ToList().ForEach(f =>
                    {
                        var fInfo = new FileInfo(f);
                        var _ = fInfo.Exists && fInfo.Length > 0 ?
                            conf.AddJsonFile(f, optional: true, reloadOnChange: true) : null;
                    });
            }).TapError(e => Console.WriteLine($"Error when trying to add *.json config files into the IConfiguration : {e} "));
        })
        .ConfigureLogging(logging => logging.ClearProviders())
        .UseSerilog((h, l) =>
        {
            Result.Try(() =>
            {
                var config = new EcsTextFormatterConfiguration
                {
                    MapCustom = (ecsDoc, logEvent) =>
                    {
                        ecsDoc.Service ??= new Service(); // Ensure Service is initialized
                        ecsDoc.Service.Name = Agent.Config.ServiceName; // Assign the service name
                        return ecsDoc;
                    }
                };

                l.ReadFrom.Configuration(h.Configuration)
                .Enrich.WithElasticApmCorrelationInfo()
                .WriteTo.Elasticsearch(new ElasticsearchSinkOptions(new Uri(h.Configuration.Get<ConfigurationLogProvider>("ElasticSearchLog")?.ElasticSearchLog ?? "http://localhost:9200"))
                {
                    AutoRegisterTemplate = true,
                    IndexFormat = "mslogs-{0:yyyy.MM.dd}",
                    DetectElasticsearchVersion = true,
                    RegisterTemplateFailure = RegisterTemplateRecovery.IndexAnyway,
                    AutoRegisterTemplateVersion = AutoRegisterTemplateVersion.ESv7,
                    FailureCallback = (l, e) => Console.WriteLine($"Unable to submit event {l?.RenderMessage()} to ElasticSearch. Exception : " + e?.ToString()),
                    EmitEventFailure = EmitEventFailureHandling.WriteToSelfLog |
                                EmitEventFailureHandling.WriteToFailureSink |
                                EmitEventFailureHandling.RaiseCallback,
                    BufferCleanPayload = (failingEvent, statuscode, exception) =>
                    {
                        return failingEvent;
                    },
                    CustomFormatter = new EcsTextFormatter(config)
                });
            }).TapError(e => Console.WriteLine($"Error when trying to UseSerilog config extension method : {e}"));

        }, writeToProviders: true); // fix https://github.com/elastic/apm-agent-dotnet/issues/1901
}
