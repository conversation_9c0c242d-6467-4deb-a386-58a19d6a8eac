﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net10.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <InvariantGlobalization>true</InvariantGlobalization>
    <PublishAot>true</PublishAot>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>..\..</DockerfileContext>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CSharpFunctionalExtensions" Version="3.6.0" />
    <PackageReference Include="Elastic.Apm" Version="1.34.1" />
    <PackageReference Include="Elastic.Apm.AspNetCore" Version="1.34.1" />
    <PackageReference Include="Elastic.Apm.MongoDb" Version="1.34.1" />
    <PackageReference Include="Elastic.Apm.SerilogEnricher" Version="9.0.0" />
    <PackageReference Include="Elastic.Serilog.Sinks" Version="9.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="10.0.0-rc.1.25451.107" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.23.0-preview.4" />
    <PackageReference Include="Serilog" Version="4.3.0" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="9.0.0" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="9.0.0" />
    <PackageReference Include="Serilog.Sinks.Elasticsearch" Version="10.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="9.0.4" />
  </ItemGroup>
  
</Project>
