using ApiAotTest.Domain;
using System.Text.Json.Serialization;

namespace ApiAotTest.Infrastructure;

[JsonSerializable(typeof(Todo[]))]
[JsonSerializable(typeof(Todo))]
[JsonSerializable(typeof(ConfigurationLogProvider))]
[JsonSerializable(typeof(string))]
[JsonSerializable(typeof(int))]
[JsonSerializable(typeof(bool))]
[JsonSerializable(typeof(double))]
[JsonSerializable(typeof(DateTime))]
[JsonSerializable(typeof(DateOnly))]
// Add more types here as needed for your configuration objects
internal partial class AppJsonSerializerContext : JsonSerializerContext
{

}
