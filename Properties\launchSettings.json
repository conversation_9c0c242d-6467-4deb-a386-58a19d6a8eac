{"profiles": {"http": {"commandName": "Project", "launchBrowser": true, "launchUrl": "apiaottest/swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "dotnetRunMessages": true, "applicationUrl": "http://localhost:5084"}, "Container (Dockerfile)": {"commandName": "<PERSON>er", "launchBrowser": true, "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}/apiaottest/swagger", "publishAllPorts": true, "useSSL": false}}, "$schema": "https://json.schemastore.org/launchsettings.json"}