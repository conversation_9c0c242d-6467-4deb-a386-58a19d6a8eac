using ApiAotTest.Domain;
using ApiAotTest.Infrastructure;
using Elastic.Transport;
using Microsoft.AspNetCore.Http.HttpResults;
using static Microsoft.AspNetCore.Http.TypedResults;

var builder = WebApplication.CreateSlimBuilder(args);

builder.Services.ConfigureHttpJsonOptions(options =>
{
    options.SerializerOptions.TypeInfoResolverChain.Insert(0, AppJsonSerializerContext.Default);
});

// Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
builder.Services.AddOpenApi();

var app = builder.Build();

var appPrefix = app.Configuration.GetValue<string>("Prefix");

app.MapOpenApi("/{appPrefix}/openapi/{documentName}.json");
app.UseSwaggerUI(options =>
{
    options.RoutePrefix = $"{appPrefix}/swagger";
    options.SwaggerEndpoint($"/{appPrefix}/openapi/v1.json", "v1");
});


Todo[] sampleTodos =
[
    new(1, "Walk the dog"),
    new(2, "Do the dishes", DateOnly.FromDateTime(DateTime.Now)),
    new(3, "Do the laundry", DateOnly.FromDateTime(DateTime.Now.AddDays(1))),
    new(4, "Clean the bathroom"),
    new(5, "Clean the car", DateOnly.FromDateTime(DateTime.Now.AddDays(2)))
];

// its like then default route prefix in asp.net core controllers
var apiRoot = app.MapGroup($"/{appPrefix}/api/v1");

var todosApi = apiRoot.MapGroup("/todos");
todosApi.MapGet("/", () => sampleTodos)
        .WithName("GetTodos");

todosApi.MapGet("/{id}", Results<Ok<Todo>, NotFound> (int id) =>
    Array.Find(sampleTodos,(a => a.Id == id)) is { } todo
        ? Ok(todo)
        : NotFound())
    .WithName("GetTodoById");

await app.RunAsync();
